<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Citations Issue</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .citation {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            font-size: 12px;
            background-color: #2563eb;
            color: white;
            border-radius: 50%;
            margin: 0 2px;
            cursor: pointer;
            border: none;
            vertical-align: super;
            position: relative;
        }
        .citation:hover {
            background-color: #1d4ed8;
        }
        .tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: #1f2937;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 11px;
            margin-bottom: 5px;
            display: none;
            z-index: 1000;
            max-width: 300px;
            white-space: normal;
        }
        .citation:hover .tooltip {
            display: block;
        }
        .code {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            border: 1px solid #e9ecef;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <h1>🔍 Debug Citations Issue</h1>
    
    <div class="section">
        <h2>1. Test Backend API</h2>
        <button onclick="testBackend()">Test Chat API</button>
        <div id="backend-result" class="code">Click to test...</div>
    </div>

    <div class="section">
        <h2>2. Test Citation Parsing Logic</h2>
        <button onclick="testParsing()">Test Parsing</button>
        <div id="parsing-result" class="code">Click to test...</div>
    </div>

    <div class="section">
        <h2>3. Test Citation Display</h2>
        <button onclick="testDisplay()">Test Display</button>
        <div id="display-result">Click to test...</div>
    </div>

    <div class="section">
        <h2>4. Manual Citation Test</h2>
        <p>This text has manual citations: Google Cloud focuses on three areas 
        <button class="citation" onclick="jumpToTime(15)">
            1
            <div class="tooltip">Jump to 00:15 - "innovating in three key areas..."</div>
        </button>
        and introduces AI agents 
        <button class="citation" onclick="jumpToTime(30)">
            2
            <div class="tooltip">Jump to 00:30 - "build your agents, our Agent Engine..."</div>
        </button>
        for developers.</p>
    </div>

    <div class="section">
        <h2>5. Frontend Integration Test</h2>
        <button onclick="testFrontendIntegration()">Test Frontend</button>
        <div id="frontend-result" class="code">Click to test...</div>
    </div>

    <script>
        let testData = null;

        function jumpToTime(seconds) {
            alert(`Would jump to ${seconds} seconds (${formatTime(seconds)})`);
        }

        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = seconds % 60;
            return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        async function testBackend() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.innerHTML = 'Testing backend...';
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/chat/message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        video_id: 11,
                        message: 'What are the main features?'
                    })
                });
                
                const data = await response.json();
                testData = data;
                
                resultDiv.innerHTML = `✅ Backend Response:
Status: ${response.status}
Response Length: ${data.response?.length || 0} chars
Citations Count: ${data.citations?.length || 0}
Contains [timestamps]: ${data.response?.includes('[') ? 'Yes' : 'No'}

Sample Response:
${data.response?.substring(0, 300) || 'No response'}...

Sample Citations:
${data.citations?.slice(0, 3).map((c, i) => 
    `${i+1}. ${c.timestamp} (${c.time}s): ${c.text.substring(0, 50)}...`
).join('\n') || 'No citations'}`;
                resultDiv.className = 'code success';
                
            } catch (error) {
                resultDiv.innerHTML = `❌ Backend Error: ${error.message}`;
                resultDiv.className = 'code error';
            }
        }

        function testParsing() {
            const resultDiv = document.getElementById('parsing-result');
            
            if (!testData) {
                resultDiv.innerHTML = '❌ No test data. Run Backend Test first.';
                resultDiv.className = 'code error';
                return;
            }
            
            const { response, citations } = testData;
            
            // Test the exact parsing logic from FormattedMessage
            const timestampPattern = /\[(\d{1,2}:\d{2}(?::\d{2})?)\]/g;
            const matches = [];
            let match;
            
            while ((match = timestampPattern.exec(response)) !== null) {
                const timestamp = match[1];
                const citation = citations.find(c => c.timestamp === timestamp);
                matches.push({
                    timestamp,
                    position: match.index,
                    citationFound: !!citation,
                    citationData: citation
                });
            }
            
            resultDiv.innerHTML = `✅ Parsing Test Results:
Regex Pattern: ${timestampPattern}
Total Matches Found: ${matches.length}
Available Citations: ${citations.length}

Matches:
${matches.map((m, i) => 
    `${i+1}. Timestamp: ${m.timestamp}
   Position: ${m.position}
   Citation Found: ${m.citationFound}
   Citation ID: ${m.citationData?.citation_id || 'N/A'}`
).join('\n')}

First 5 Available Citations:
${citations.slice(0, 5).map((c, i) => 
    `${i+1}. ${c.timestamp} -> ID: ${c.citation_id}`
).join('\n')}`;
            resultDiv.className = 'code success';
        }

        function testDisplay() {
            const resultDiv = document.getElementById('display-result');
            
            if (!testData) {
                resultDiv.innerHTML = '<div class="result error">❌ No test data. Run Backend Test first.</div>';
                return;
            }
            
            const { response, citations } = testData;
            
            // Simulate the FormattedMessage component logic
            const timestampPattern = /\[(\d{1,2}:\d{2}(?::\d{2})?)\]/g;
            let processedText = response.substring(0, 500); // First 500 chars for testing
            let citationCount = 0;
            
            processedText = processedText.replace(timestampPattern, (match, timestamp) => {
                const citation = citations.find(c => c.timestamp === timestamp);
                if (citation) {
                    citationCount++;
                    return `<button class="citation" onclick="jumpToTime(${citation.time})" title="Jump to ${citation.timestamp}">
                        ${citation.citation_id}
                        <div class="tooltip">
                            🕐 ${citation.timestamp}<br>
                            ${citation.text.substring(0, 100)}...
                        </div>
                    </button>`;
                }
                return match;
            });
            
            resultDiv.innerHTML = `
                <div class="result success">
                    ✅ Display Test Results:<br>
                    Citations Processed: ${citationCount}<br>
                    Available Citations: ${citations.length}
                </div>
                <div style="line-height: 1.8; padding: 15px; background: #f8f9fa; border-radius: 6px;">
                    ${processedText}...
                </div>
            `;
        }

        function testFrontendIntegration() {
            const resultDiv = document.getElementById('frontend-result');
            
            // Test if the main app is accessible
            resultDiv.innerHTML = 'Testing frontend integration...';
            
            try {
                // Check if we can access the main app
                const mainAppUrl = 'http://localhost:8081';
                
                resultDiv.innerHTML = `✅ Frontend Integration Test:
Main App URL: ${mainAppUrl}
Current Page: ${window.location.href}

To test citations in the main app:
1. Go to ${mainAppUrl}
2. Upload a video or use video ID 11
3. Go to Chat tab
4. Send a message like "What are the main features?"
5. Check if citations appear as clickable blue circles

Expected Behavior:
- Citations should appear as small blue circles with numbers
- Hovering should show tooltip with timestamp and text
- Clicking should jump to that time in the video

If citations don't appear:
- Check browser console for errors
- Verify FormattedMessage component is receiving citations
- Check if timestamp regex is matching correctly`;
                resultDiv.className = 'code success';
                
            } catch (error) {
                resultDiv.innerHTML = `❌ Frontend Integration Error: ${error.message}`;
                resultDiv.className = 'code error';
            }
        }
    </script>
</body>
</html>
