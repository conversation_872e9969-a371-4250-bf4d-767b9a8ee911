"""
Query Analyzer for Multi-Modal Search
Analyzes user queries to determine optimal search strategy
"""

import re
from typing import Dict, <PERSON>, Tuple, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class QueryAnalysis:
    original_query: str
    query_type: str  # visual, temporal, conceptual, counting, text
    visual_attributes: List[str]  # colors, sizes, shapes
    temporal_attributes: List[str]  # time-related terms
    objects: List[str]  # detected objects
    actions: List[str]  # detected actions
    confidence: float  # confidence in analysis
    suggested_method: str  # recommended search method
    enhanced_query: str  # optimized query for search

class QueryAnalyzer:
    """Analyzes queries to optimize search strategy"""
    
    def __init__(self):
        # Dynamic pattern matching without hardcoded vocabularies
        # The analyzer will work with any content from the video
        pass
    
    def analyze_query(self, query: str) -> QueryAnalysis:
        """Analyze query to determine optimal search strategy"""
        query_lower = query.lower()
        words = query_lower.split()
        
        # Extract attributes
        visual_attrs = self._extract_visual_attributes(query_lower)
        temporal_attrs = self._extract_temporal_attributes(query_lower)
        objects = self._extract_objects(query_lower)
        actions = self._extract_actions(query_lower)
        
        # Determine query type
        query_type = self._determine_query_type(
            query_lower, visual_attrs, temporal_attrs, objects, actions
        )
        
        # Suggest search method
        suggested_method = self._suggest_search_method(query_type, visual_attrs)
        
        # Enhance query for better search
        enhanced_query = self._enhance_query(
            query, query_type, visual_attrs, objects, actions
        )
        
        # Calculate confidence
        confidence = self._calculate_confidence(
            visual_attrs, temporal_attrs, objects, actions
        )
        
        return QueryAnalysis(
            original_query=query,
            query_type=query_type,
            visual_attributes=visual_attrs,
            temporal_attributes=temporal_attrs,
            objects=objects,
            actions=actions,
            confidence=confidence,
            suggested_method=suggested_method,
            enhanced_query=enhanced_query
        )
    
    def _extract_visual_attributes(self, query: str) -> List[str]:
        """Extract visual attributes from query dynamically"""
        attributes = []
        
        # Dynamic pattern matching for visual attributes
        # Look for words that might describe visual properties
        import re
        
        # Pattern for potential color words (words before "color" or "colored")
        color_pattern = r'\b(\w+)\s+(?:color|colored|colour)\b'
        color_matches = re.findall(color_pattern, query, re.IGNORECASE)
        for match in color_matches:
            attributes.append(f"color:{match.lower()}")
        
        # Pattern for size descriptors (words before size-related nouns)
        size_pattern = r'\b(\w+)\s+(?:size|sized)\b'
        size_matches = re.findall(size_pattern, query, re.IGNORECASE)
        for match in size_matches:
            attributes.append(f"size:{match.lower()}")
        
        # Pattern for shape descriptors
        shape_pattern = r'\b(\w+)\s+(?:shape|shaped)\b'
        shape_matches = re.findall(shape_pattern, query, re.IGNORECASE)
        for match in shape_matches:
            attributes.append(f"shape:{match.lower()}")
        
        return attributes
    
    def _extract_temporal_attributes(self, query: str) -> List[str]:
        """Extract temporal attributes from query dynamically"""
        attributes = []
        import re
        
        # Extract time patterns dynamically
        time_patterns = [
            (r'at (\d+:\d+)', 'specific_time'),
            (r'after (\d+) (seconds?|minutes?)', 'relative_time'),
            (r'before (\d+) (seconds?|minutes?)', 'relative_time'),
            (r'for (\d+) (seconds?|minutes?)', 'duration'),
            (r'(\d+) seconds? (?:into|in)', 'timestamp'),
            (r'from (\d+:\d+) to (\d+:\d+)', 'time_range')
        ]
        
        for pattern, attr_type in time_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            for match in matches:
                attributes.append(f"{attr_type}:{match}")
        
        # Look for temporal indicators in context
        temporal_indicators = r'\b(beginning|start|end|middle|throughout|during|after|before|when|while|first|last|next)\b'
        temporal_matches = re.findall(temporal_indicators, query, re.IGNORECASE)
        attributes.extend(temporal_matches)
        
        return attributes
    
    def _extract_objects(self, query: str) -> List[str]:
        """Extract potential objects from query dynamically"""
        objects = []
        import re
        
        # Pattern to find nouns (potential objects) after determiners
        # This captures what the user is actually looking for
        noun_patterns = [
            r'\b(?:a|an|the|some|any|every|all)\s+(\w+)\b',
            r'\bfind\s+(?:a|an|the|some|any|all)?\s*(\w+)\b',
            r'\blook\s+for\s+(?:a|an|the|some|any)?\s*(\w+)\b',
            r'\bsearch\s+for\s+(?:a|an|the|some|any)?\s*(\w+)\b'
        ]
        
        for pattern in noun_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            objects.extend([m for m in matches if m and len(m) > 2])
        
        # Extract compound phrases (adjective + noun)
        compound_pattern = r'\b(\w+\s+\w+)\b(?=\s+(?:in|on|at|near|by)|$)'
        compound_matches = re.findall(compound_pattern, query, re.IGNORECASE)
        objects.extend(compound_matches)
        
        # Extract words that might be objects based on query structure
        # This is more flexible and doesn't rely on a predefined list
        query_words = query.lower().split()
        for i, word in enumerate(query_words):
            # Skip common query words
            if word in ['find', 'search', 'look', 'for', 'show', 'where', 'when', 'how', 'many']:
                continue
            # Add words that might be objects (simple heuristic)
            if len(word) > 2 and word not in objects:
                objects.append(word)
        
        return list(set(objects))  # Remove duplicates
    
    def _extract_actions(self, query: str) -> List[str]:
        """Extract actions from query dynamically"""
        actions = []
        import re
        
        # Look for -ing words (gerunds) which often indicate actions
        ing_pattern = r'\b(\w+ing)\b'
        ing_matches = re.findall(ing_pattern, query, re.IGNORECASE)
        
        # Filter out common non-action -ing words
        non_actions = {'something', 'nothing', 'anything', 'everything', 'thing'}
        actions.extend([m for m in ing_matches if m.lower() not in non_actions])
        
        # Extract action phrases dynamically
        action_patterns = [
            r'(\w+)\s+(\w+ing)',  # noun + action
            r'(\w+ing)\s+(\w+)',  # action + noun
            r'\b(\w+)\s+(?:is|are|was|were)\s+(\w+ing)\b'  # subject + verb + action
        ]
        
        for pattern in action_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            for match in matches:
                action_phrase = ' '.join(match)
                if action_phrase not in actions:
                    actions.append(action_phrase)
        
        return list(set(actions))
    
    def _determine_query_type(self, query: str, visual_attrs: List[str], 
                            temporal_attrs: List[str], objects: List[str], 
                            actions: List[str]) -> str:
        """Determine the primary type of query dynamically"""
        query_lower = query.lower()
        
        # Check for counting query patterns
        counting_patterns = r'\b(how many|count|number of|amount of|total)\b'
        if re.search(counting_patterns, query_lower, re.IGNORECASE):
            return "counting"
        
        # Check for text/OCR query
        text_patterns = r'\b(text|sign|writing|words?|read|label|caption)\b'
        if re.search(text_patterns, query_lower, re.IGNORECASE):
            return "text"
        
        # Check for temporal query
        if len(temporal_attrs) >= 2 or 'throughout' in query_lower or 'timeline' in query_lower:
            return "temporal"
        
        # Check for visual query
        if len(visual_attrs) >= 1 or (objects and any('color:' in attr for attr in visual_attrs)):
            return "visual"
        
        # Check for action query
        if actions:
            return "action"
        
        # Default to general search
        return "general"
    
    def _suggest_search_method(self, query_type: str, visual_attrs: List[str]) -> str:
        """Suggest the best search method based on query analysis"""
        
        if query_type == "counting":
            return "native_temporal_counting"
        
        if query_type == "text":
            return "native_text_detection"
        
        if query_type == "visual" and len(visual_attrs) >= 2:
            return "clip_visual_search"
        
        if query_type == "temporal":
            return "temporal_scene_search"
        
        if query_type == "action":
            return "native_video_search"
        
        # Default to hybrid search
        return "hybrid_search"
    
    def _enhance_query(self, query: str, query_type: str, 
                      visual_attrs: List[str], objects: List[str], 
                      actions: List[str]) -> str:
        """Enhance query for better search results"""
        
        if query_type == "visual":
            # Add visual context
            enhanced_parts = [query]
            
            # Add implied visual terms
            if objects and not any('color:' in attr for attr in visual_attrs):
                enhanced_parts.append("visible")
            
            return " ".join(enhanced_parts)
        
        if query_type == "counting":
            # Clarify counting intent
            if "how many" not in query.lower():
                return f"count {query}"
        
        if query_type == "action":
            # Add action context
            if not any(word in query.lower() for word in ['showing', 'doing', 'performing']):
                return f"{query} action"
        
        # Return original if no enhancement needed
        return query
    
    def _calculate_confidence(self, visual_attrs: List[str], 
                            temporal_attrs: List[str], 
                            objects: List[str], 
                            actions: List[str]) -> float:
        """Calculate confidence in query analysis"""
        
        # Base confidence
        confidence = 0.5
        
        # Increase confidence for detected attributes
        if visual_attrs:
            confidence += 0.1 * min(len(visual_attrs), 3)
        
        if temporal_attrs:
            confidence += 0.1 * min(len(temporal_attrs), 2)
        
        if objects:
            confidence += 0.15 * min(len(objects), 2)
        
        if actions:
            confidence += 0.1 * min(len(actions), 2)
        
        return min(0.95, confidence)
    
    def get_query_suggestions(self, query: str) -> List[str]:
        """Generate query suggestions dynamically based on query analysis"""
        analysis = self.analyze_query(query)
        suggestions = []
        
        # Suggest adding visual attributes
        if analysis.query_type == "visual" and not analysis.visual_attributes:
            suggestions.append(f"Try adding descriptive terms: '{query} with color' or '{query} large size'")
        
        # Suggest temporal context
        if not analysis.temporal_attributes and analysis.objects:
            suggestions.append(f"Try adding when: '{query} at 2:30' or '{query} in the beginning'")
        
        # Suggest being more specific about what to find
        if len(analysis.objects) == 0:
            suggestions.append(f"Try describing what you're looking for: 'find {query}' or 'show {query}'")
        
        return suggestions