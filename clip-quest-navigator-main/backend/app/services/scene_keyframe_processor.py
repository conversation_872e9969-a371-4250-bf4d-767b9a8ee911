"""
Scene-Based Keyframe Processor
Intelligently selects 3-5% of frames as keyframes and processes them with CLIP + optical flow
"""

import logging
import async<PERSON>
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
import numpy as np
import cv2
from pathlib import Path

from app.core.config import settings
from app.services.scene_detector import SceneDetector
from app.services.clip_optical_flow_service import CLIPOpticalFlowService, EnhancedEmbedding
from app.services.resilient_gemini_service import ResilientGeminiService
from app.models.video import VideoFrame
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

@dataclass
class ProcessedKeyframe:
    """Processed keyframe with all embeddings and analysis"""
    frame_id: str
    timestamp: float
    frame_path: str
    scene_id: int
    scene_change_score: float
    transition_type: str
    description: str
    embedding: EnhancedEmbedding
    metadata: Dict

class SceneKeyframeProcessor:
    """Main processor for scene-based keyframe selection and embedding"""
    
    def __init__(self, gemini_service=None):
        # Initialize services
        self.scene_detector = SceneDetector()
        self.clip_flow_service = CLIPOpticalFlowService()
        self.gemini_service = ResilientGeminiService(gemini_service) if gemini_service else None
        
        # Configuration from settings
        self.keyframe_percentage = settings.SCENE_KEYFRAME_PERCENTAGE
        self.max_keyframes = int(settings.GEMINI_MAX_VIDEO_FRAMES * self.keyframe_percentage)
        
    async def process_video(self, 
                          video_path: str, 
                          video_id: int,
                          db: Session) -> List[ProcessedKeyframe]:
        """Process video to extract and analyze keyframes"""
        try:
            logger.info(f"🎬 Processing video {video_id} for keyframe extraction")
            
            # Step 1: Detect scene changes
            logger.info("🔍 Detecting scene changes...")
            scene_results = self._detect_scenes(video_path)
            
            # Step 2: Select keyframes (3-5% of total frames)
            logger.info(f"📸 Selecting {self.keyframe_percentage*100:.1f}% keyframes...")
            keyframes = self._select_keyframes(scene_results)
            logger.info(f"✅ Selected {len(keyframes)} keyframes from {len(scene_results)} frames")
            
            # Step 3: Extract keyframe images
            logger.info("💾 Extracting keyframe images...")
            keyframe_paths = await self._extract_keyframes(video_path, keyframes, video_id)
            
            # Step 4: Generate CLIP + optical flow embeddings
            logger.info("🧠 Generating CLIP + optical flow embeddings...")
            embeddings = await self._generate_embeddings(keyframe_paths, video_path)
            
            # Step 5: Analyze keyframes with Gemini
            logger.info("🤖 Analyzing keyframes with Gemini...")
            descriptions = await self._analyze_keyframes(keyframe_paths)
            
            # Step 6: Store processed keyframes
            logger.info("💾 Storing processed keyframes...")
            processed_keyframes = await self._store_keyframes(
                keyframes, keyframe_paths, embeddings, descriptions, video_id, db
            )
            
            logger.info(f"✅ Successfully processed {len(processed_keyframes)} keyframes")
            return processed_keyframes
            
        except Exception as e:
            logger.error(f"Error processing video: {e}")
            raise
    
    def _detect_scenes(self, video_path: str) -> List[Dict]:
        """Detect scene changes in video"""
        try:
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            frames = []
            frame_paths = []
            timestamps = []
            
            # Sample frames for scene detection
            sample_interval = max(1, total_frames // 1000)  # Max 1000 samples
            
            frame_idx = 0
            while cap.isOpened() and frame_idx < total_frames:
                ret, frame = cap.read()
                if not ret:
                    break
                
                if frame_idx % sample_interval == 0:
                    timestamp = frame_idx / fps
                    frames.append(frame)
                    timestamps.append(timestamp)
                    frame_paths.append(f"frame_{frame_idx}")
                
                frame_idx += 1
            
            cap.release()
            
            # Detect scenes
            scene_results = self.scene_detector.detect_scene_changes(frame_paths, timestamps)
            
            # Enhance results with frames
            for i, result in enumerate(scene_results):
                if i < len(frames):
                    result.frame = frames[i]
            
            return scene_results
            
        except Exception as e:
            logger.error(f"Error detecting scenes: {e}")
            return []
    
    def _select_keyframes(self, scene_results: List) -> List[Dict]:
        """Select keyframes based on scene changes"""
        # First, get all keyframes identified by scene detector
        keyframes = self.scene_detector.select_keyframes(scene_results)
        
        # Ensure we're within the target percentage (3-5%)
        total_frames = len(scene_results)
        target_keyframes = int(total_frames * self.keyframe_percentage)
        
        # If we have too many, prioritize by scene change score
        if len(keyframes) > target_keyframes:
            keyframes.sort(key=lambda x: x.scene_change_score, reverse=True)
            keyframes = keyframes[:target_keyframes]
        
        # If we have too few, add more frames with high motion or scene scores
        elif len(keyframes) < target_keyframes * 0.75:  # Allow some flexibility
            non_keyframes = [r for r in scene_results if not r.is_keyframe]
            non_keyframes.sort(key=lambda x: x.scene_change_score, reverse=True)
            additional_needed = target_keyframes - len(keyframes)
            keyframes.extend(non_keyframes[:additional_needed])
        
        # Sort by timestamp
        keyframes.sort(key=lambda x: x.timestamp)
        
        return keyframes
    
    async def _extract_keyframes(self, video_path: str, keyframes: List, video_id: int) -> List[Dict]:
        """Extract keyframe images from video"""
        keyframe_paths = []
        cap = cv2.VideoCapture(video_path)
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        # Create output directory
        output_dir = Path(f"uploads/keyframes/video_{video_id}")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            for kf in keyframes:
                # Seek to frame
                frame_number = int(kf.timestamp * fps)
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                
                ret, frame = cap.read()
                if not ret:
                    continue
                
                # Save frame
                frame_path = output_dir / f"keyframe_{kf.timestamp:.2f}.jpg"
                cv2.imwrite(str(frame_path), frame)
                
                keyframe_paths.append({
                    'timestamp': kf.timestamp,
                    'frame_path': str(frame_path),
                    'scene_id': kf.scene_id,
                    'scene_change_score': kf.scene_change_score,
                    'transition_type': kf.transition_type,
                    'frame': frame
                })
                
        finally:
            cap.release()
        
        return keyframe_paths
    
    async def _generate_embeddings(self, keyframe_paths: List[Dict], video_path: str) -> List[EnhancedEmbedding]:
        """Generate CLIP + optical flow embeddings for keyframes"""
        embeddings = []
        prev_frame = None
        
        for kf in keyframe_paths:
            frame = kf.get('frame')
            if frame is None:
                frame = cv2.imread(kf['frame_path'])
            
            embedding = await self.clip_flow_service.process_keyframe(
                frame_path=kf['frame_path'],
                frame=frame,
                timestamp=kf['timestamp'],
                prev_frame=prev_frame,
                metadata={
                    'scene_id': kf['scene_id'],
                    'scene_change_score': kf['scene_change_score'],
                    'transition_type': kf['transition_type']
                }
            )
            
            if embedding:
                embeddings.append(embedding)
            
            prev_frame = frame
        
        return embeddings
    
    async def _analyze_keyframes(self, keyframe_paths: List[Dict]) -> List[str]:
        """Analyze keyframes with Gemini to generate descriptions"""
        descriptions = []
        
        if not self.gemini_service:
            return [""] * len(keyframe_paths)
        
        # Process in batches to avoid overwhelming the API
        batch_size = 10
        for i in range(0, len(keyframe_paths), batch_size):
            batch = keyframe_paths[i:i + batch_size]
            
            # Analyze each keyframe
            batch_descriptions = []
            for kf in batch:
                try:
                    result = await self.gemini_service.analyze_frame_with_retry(
                        kf['frame_path'],
                        context=f"Keyframe at {kf['timestamp']:.1f}s"
                    )
                    description = result.get('description', '')
                    batch_descriptions.append(description)
                except Exception as e:
                    logger.error(f"Error analyzing keyframe: {e}")
                    batch_descriptions.append("")
            
            descriptions.extend(batch_descriptions)
            
            # Small delay between batches
            if i + batch_size < len(keyframe_paths):
                await asyncio.sleep(0.5)
        
        return descriptions
    
    async def _store_keyframes(self, 
                             keyframes: List,
                             keyframe_paths: List[Dict],
                             embeddings: List[EnhancedEmbedding],
                             descriptions: List[str],
                             video_id: int,
                             db: Session) -> List[ProcessedKeyframe]:
        """Store processed keyframes in database"""
        processed = []
        
        for i, (kf, kf_path, desc) in enumerate(zip(keyframes, keyframe_paths, descriptions)):
            # Find corresponding embedding
            embedding = None
            for emb in embeddings:
                if abs(emb.timestamp - kf.timestamp) < 0.1:
                    embedding = emb
                    break
            
            if not embedding:
                continue
            
            # Create or update frame in database
            frame = db.query(VideoFrame).filter(
                VideoFrame.video_id == video_id,
                VideoFrame.timestamp == kf.timestamp
            ).first()
            
            if not frame:
                frame = VideoFrame(
                    video_id=video_id,
                    timestamp=kf.timestamp,
                    frame_path=kf_path['frame_path'],
                    description=desc,
                    is_keyframe=True,
                    scene_id=kf.scene_id,
                    metadata={
                        'scene_change_score': kf.scene_change_score,
                        'transition_type': kf.transition_type,
                        'has_clip_embedding': embedding.clip_embedding is not None,
                        'has_optical_flow': embedding.optical_flow_features is not None
                    }
                )
                db.add(frame)
            else:
                frame.description = desc
                frame.is_keyframe = True
                frame.metadata = {
                    'scene_change_score': kf.scene_change_score,
                    'transition_type': kf.transition_type,
                    'has_clip_embedding': embedding.clip_embedding is not None,
                    'has_optical_flow': embedding.optical_flow_features is not None
                }
            
            processed.append(ProcessedKeyframe(
                frame_id=f"frame_{video_id}_{kf.timestamp}",
                timestamp=kf.timestamp,
                frame_path=kf_path['frame_path'],
                scene_id=kf.scene_id,
                scene_change_score=kf.scene_change_score,
                transition_type=kf.transition_type,
                description=desc,
                embedding=embedding,
                metadata=frame.metadata
            ))
        
        db.commit()
        return processed