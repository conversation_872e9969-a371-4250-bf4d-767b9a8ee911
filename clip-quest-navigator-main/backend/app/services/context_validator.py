"""
Dynamic Context Validator
Validates search matches based on actual video content without pre-assumptions
"""

import re
from typing import List, Dict, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class ContextValidator:
    """Validates search results based on dynamic video analysis"""
    
    def __init__(self):
        # No hardcoded assumptions about video types or content
        pass
    
    def detect_video_context(self, frame_descriptions: List[str]) -> Dict[str, float]:
        """
        Dynamically analyze video context from actual frame descriptions
        Returns a context analysis without pre-defined categories
        """
        if not frame_descriptions:
            return {}
        
        # Analyze actual content without assumptions
        context_analysis = {}
        word_frequency = {}
        
        # Build word frequency from actual descriptions
        for description in frame_descriptions:
            if not description:
                continue
                
            words = re.findall(r'\b\w+\b', description.lower())
            for word in words:
                if len(word) > 3:  # Skip very short words
                    word_frequency[word] = word_frequency.get(word, 0) + 1
        
        # Identify themes based on actual content
        total_frames = len(frame_descriptions)
        for word, count in word_frequency.items():
            if count > total_frames * 0.1:  # Word appears in >10% of frames
                context_analysis[f"theme_{word}"] = count / total_frames
        
        return context_analysis
    
    def validate_result_in_context(self, 
                                 query: str, 
                                 result_description: str,
                                 video_context: Dict[str, float]) -> Tuple[bool, str]:
        """
        Validate if a search result makes sense based on actual video content
        No hardcoded assumptions about what should or shouldn't be in videos
        """
        if not result_description:
            return False, "No description available"
        
        query_lower = query.lower()
        desc_lower = result_description.lower()
        
        # Simple validation: check if query terms actually appear in result
        query_words = set(re.findall(r'\b\w+\b', query_lower))
        desc_words = set(re.findall(r'\b\w+\b', desc_lower))
        
        # Check for exact matches
        matches = query_words.intersection(desc_words)
        if matches:
            return True, f"Found matches: {', '.join(matches)}"
        
        # Check for partial matches (substrings)
        for query_word in query_words:
            if len(query_word) > 3:  # Only check longer words
                if any(query_word in desc_word for desc_word in desc_words):
                    return True, f"Found partial match for '{query_word}'"
        
        return False, "No matching terms found in result"
    
    def calculate_context_penalty(self, 
                                query: str,
                                video_context: Dict[str, float]) -> float:
        """
        Calculate confidence adjustment based on how well query fits video content
        No hardcoded penalties - based on actual content analysis
        """
        if not video_context:
            return 0.0  # No penalty if no context available
        
        query_words = set(re.findall(r'\b\w+\b', query.lower()))
        
        # Check if query words appear in video themes
        relevance_score = 0.0
        for theme, weight in video_context.items():
            theme_word = theme.replace('theme_', '')
            if theme_word in query_words:
                relevance_score += weight
        
        # If query is highly relevant to video themes, no penalty
        if relevance_score > 0.2:
            return 0.0
        
        # If query is somewhat relevant, small penalty
        if relevance_score > 0.05:
            return 0.2
        
        # If query seems unrelated to main video themes, moderate penalty
        # But never more than 0.5 to avoid completely blocking results
        return min(0.5, 1.0 - relevance_score * 5)
    
    def suggest_context_appropriate_queries(self, 
                                          video_context: Dict[str, float]) -> List[str]:
        """
        Suggest queries based on actual video content
        Dynamic suggestions from what's actually in the video
        """
        suggestions = []
        
        if not video_context:
            return ["Analyze video first to get content-based suggestions"]
        
        # Sort themes by frequency
        sorted_themes = sorted(video_context.items(), key=lambda x: x[1], reverse=True)
        
        # Generate suggestions from top themes
        for theme, weight in sorted_themes[:5]:
            theme_word = theme.replace('theme_', '')
            if weight > 0.15:  # Only suggest prominent themes
                suggestions.append(theme_word)
                # Also suggest combinations if we have multiple themes
                if len(suggestions) > 1:
                    suggestions.append(f"{suggestions[-2]} and {theme_word}")
        
        # Limit suggestions
        return suggestions[:8]