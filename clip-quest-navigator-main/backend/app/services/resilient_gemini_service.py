"""
Resilient Gemini Service with Exponential Backoff and Circuit Breaker
Provides fault-tolerant access to Gemini API with automatic retry and circuit breaking
"""

import asyncio
import time
import logging
from typing import Optional, Dict, Any, Callable
from dataclasses import dataclass
from enum import Enum
import random
from functools import wraps
from app.core.config import settings

logger = logging.getLogger(__name__)

class CircuitState(Enum):
    CLOSED = "closed"  # Normal operation
    OPEN = "open"      # Failing, reject calls
    HALF_OPEN = "half_open"  # Testing if service recovered

@dataclass
class CircuitBreakerStats:
    """Statistics for circuit breaker"""
    failure_count: int = 0
    success_count: int = 0
    last_failure_time: float = 0
    last_success_time: float = 0
    state: CircuitState = CircuitState.CLOSED
    state_changed_at: float = 0

class CircuitBreaker:
    """Circuit breaker implementation"""
    
    def __init__(self):
        self.stats = CircuitBreakerStats()
        self.failure_threshold = settings.CIRCUIT_BREAKER_THRESHOLD
        self.timeout = settings.CIRCUIT_BREAKER_TIMEOUT
        self.half_open_requests = 0
        self.max_half_open_requests = 3
    
    def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection"""
        if self.stats.state == CircuitState.OPEN:
            # Check if timeout expired
            if time.time() - self.stats.state_changed_at > self.timeout:
                self._transition_to_half_open()
            else:
                raise Exception("Circuit breaker is OPEN - service unavailable")
        
        if self.stats.state == CircuitState.HALF_OPEN:
            if self.half_open_requests >= self.max_half_open_requests:
                raise Exception("Circuit breaker is HALF_OPEN - limited requests only")
            self.half_open_requests += 1
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise e
    
    async def async_call(self, func: Callable, *args, **kwargs):
        """Execute async function with circuit breaker protection"""
        if self.stats.state == CircuitState.OPEN:
            if time.time() - self.stats.state_changed_at > self.timeout:
                self._transition_to_half_open()
            else:
                raise Exception("Circuit breaker is OPEN - service unavailable")
        
        if self.stats.state == CircuitState.HALF_OPEN:
            if self.half_open_requests >= self.max_half_open_requests:
                raise Exception("Circuit breaker is HALF_OPEN - limited requests only")
            self.half_open_requests += 1
        
        try:
            result = await func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise e
    
    def _on_success(self):
        """Handle successful call"""
        self.stats.success_count += 1
        self.stats.last_success_time = time.time()
        
        if self.stats.state == CircuitState.HALF_OPEN:
            # Transition back to closed after successful calls
            if self.stats.success_count >= self.max_half_open_requests:
                self._transition_to_closed()
    
    def _on_failure(self):
        """Handle failed call"""
        self.stats.failure_count += 1
        self.stats.last_failure_time = time.time()
        
        if self.stats.state == CircuitState.HALF_OPEN:
            self._transition_to_open()
        elif self.stats.state == CircuitState.CLOSED:
            if self.stats.failure_count >= self.failure_threshold:
                self._transition_to_open()
    
    def _transition_to_open(self):
        """Transition to OPEN state"""
        logger.warning("Circuit breaker transitioning to OPEN state")
        self.stats.state = CircuitState.OPEN
        self.stats.state_changed_at = time.time()
        self.half_open_requests = 0
    
    def _transition_to_half_open(self):
        """Transition to HALF_OPEN state"""
        logger.info("Circuit breaker transitioning to HALF_OPEN state")
        self.stats.state = CircuitState.HALF_OPEN
        self.stats.state_changed_at = time.time()
        self.stats.failure_count = 0
        self.stats.success_count = 0
        self.half_open_requests = 0
    
    def _transition_to_closed(self):
        """Transition to CLOSED state"""
        logger.info("Circuit breaker transitioning to CLOSED state")
        self.stats.state = CircuitState.CLOSED
        self.stats.state_changed_at = time.time()
        self.stats.failure_count = 0
        self.stats.success_count = 0
        self.half_open_requests = 0

def exponential_backoff_retry(max_retries: int = None, 
                            initial_backoff: float = None,
                            max_backoff: float = None,
                            multiplier: float = None):
    """Decorator for exponential backoff retry logic"""
    max_retries = max_retries or settings.GEMINI_MAX_RETRIES
    initial_backoff = initial_backoff or settings.GEMINI_INITIAL_BACKOFF
    max_backoff = max_backoff or settings.GEMINI_MAX_BACKOFF
    multiplier = multiplier or settings.GEMINI_BACKOFF_MULTIPLIER
    
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            backoff = initial_backoff
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    # Check if retryable
                    if not _is_retryable_error(e):
                        raise e
                    
                    if attempt < max_retries - 1:
                        # Add jitter to prevent thundering herd
                        jitter = random.uniform(0, backoff * 0.1)
                        sleep_time = min(backoff + jitter, max_backoff)
                        
                        logger.warning(
                            f"Attempt {attempt + 1}/{max_retries} failed: {e}. "
                            f"Retrying in {sleep_time:.2f}s..."
                        )
                        
                        await asyncio.sleep(sleep_time)
                        backoff *= multiplier
                    else:
                        logger.error(f"All {max_retries} attempts failed. Last error: {e}")
            
            raise last_exception
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            backoff = initial_backoff
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if not _is_retryable_error(e):
                        raise e
                    
                    if attempt < max_retries - 1:
                        jitter = random.uniform(0, backoff * 0.1)
                        sleep_time = min(backoff + jitter, max_backoff)
                        
                        logger.warning(
                            f"Attempt {attempt + 1}/{max_retries} failed: {e}. "
                            f"Retrying in {sleep_time:.2f}s..."
                        )
                        
                        time.sleep(sleep_time)
                        backoff *= multiplier
                    else:
                        logger.error(f"All {max_retries} attempts failed. Last error: {e}")
            
            raise last_exception
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def _is_retryable_error(error: Exception) -> bool:
    """Determine if an error is retryable"""
    error_str = str(error).lower()
    
    # Retryable errors
    retryable_patterns = [
        "rate limit",
        "quota exceeded",
        "timeout",
        "connection",
        "temporary",
        "unavailable",
        "overloaded",
        "429",  # Too Many Requests
        "500",  # Internal Server Error
        "502",  # Bad Gateway
        "503",  # Service Unavailable
        "504",  # Gateway Timeout
    ]
    
    # Non-retryable errors
    non_retryable_patterns = [
        "invalid api key",
        "unauthorized",
        "forbidden",
        "not found",
        "bad request",
        "400",  # Bad Request
        "401",  # Unauthorized
        "403",  # Forbidden
        "404",  # Not Found
    ]
    
    # Check non-retryable first
    for pattern in non_retryable_patterns:
        if pattern in error_str:
            return False
    
    # Check retryable
    for pattern in retryable_patterns:
        if pattern in error_str:
            return True
    
    # Default to retryable for unknown errors
    return True

class ResilientGeminiService:
    """Wrapper for Gemini service with resilience features"""
    
    def __init__(self, gemini_service):
        self.gemini_service = gemini_service
        self.circuit_breaker = CircuitBreaker()
    
    @exponential_backoff_retry()
    async def analyze_frame_with_retry(self, *args, **kwargs):
        """Analyze frame with retry logic and circuit breaker"""
        return await self.circuit_breaker.async_call(
            self.gemini_service.analyze_frame, *args, **kwargs
        )
    
    @exponential_backoff_retry()
    async def analyze_video_with_retry(self, *args, **kwargs):
        """Analyze video with retry logic and circuit breaker"""
        return await self.circuit_breaker.async_call(
            self.gemini_service.analyze_video, *args, **kwargs
        )
    
    @exponential_backoff_retry()
    async def generate_chat_response_with_retry(self, *args, **kwargs):
        """Generate chat response with retry logic and circuit breaker"""
        return await self.circuit_breaker.async_call(
            self.gemini_service.generate_chat_response, *args, **kwargs
        )
    
    def get_circuit_breaker_status(self) -> Dict[str, Any]:
        """Get current circuit breaker status"""
        return {
            "state": self.circuit_breaker.stats.state.value,
            "failure_count": self.circuit_breaker.stats.failure_count,
            "success_count": self.circuit_breaker.stats.success_count,
            "last_failure": self.circuit_breaker.stats.last_failure_time,
            "last_success": self.circuit_breaker.stats.last_success_time,
        }