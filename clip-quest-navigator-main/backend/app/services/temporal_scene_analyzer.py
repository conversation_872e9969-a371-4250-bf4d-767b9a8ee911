"""
Temporal Scene Analyzer for Better Video Understanding
Analyzes scenes with temporal context for improved search accuracy
"""

import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional
import logging
from dataclasses import dataclass
from collections import deque

logger = logging.getLogger(__name__)

@dataclass
class SceneSegment:
    start_time: float
    end_time: float
    scene_type: str  # 'static', 'dynamic', 'transition'
    dominant_colors: List[str]
    motion_level: float  # 0-1
    object_persistence: Dict[str, float]  # object -> duration in scene
    description: str

class TemporalSceneAnalyzer:
    """Analyzes video scenes with temporal context"""
    
    def __init__(self):
        self.frame_buffer = deque(maxlen=30)  # 1 second buffer at 30fps
        self.scene_segments = []
        self.object_tracker = {}  # Track objects across frames
        
    def analyze_video_scenes(self, video_path: str, sample_rate: float = 1.0) -> List[SceneSegment]:
        """Analyze video and extract scene segments with temporal understanding"""
        try:
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frame_count / fps if fps > 0 else 0
            
            # Sample frames based on rate
            sample_interval = int(fps * sample_rate)
            
            current_scene_start = 0
            previous_histogram = None
            scene_frames = []
            
            frame_idx = 0
            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break
                
                if frame_idx % sample_interval == 0:
                    timestamp = frame_idx / fps
                    
                    # Calculate frame features
                    histogram = self._calculate_color_histogram(frame)
                    motion = self._calculate_motion_score(frame)
                    
                    # Detect scene change
                    if previous_histogram is not None:
                        hist_diff = cv2.compareHist(previous_histogram, histogram, cv2.HISTCMP_CORREL)
                        
                        # Scene change detected
                        if hist_diff < 0.7:  # Threshold for scene change
                            # Process accumulated scene
                            if scene_frames:
                                segment = self._process_scene_segment(
                                    scene_frames, 
                                    current_scene_start, 
                                    timestamp
                                )
                                self.scene_segments.append(segment)
                            
                            # Start new scene
                            current_scene_start = timestamp
                            scene_frames = []
                    
                    # Add frame to current scene
                    scene_frames.append({
                        'frame': frame,
                        'timestamp': timestamp,
                        'histogram': histogram,
                        'motion': motion
                    })
                    
                    previous_histogram = histogram
                
                frame_idx += 1
            
            # Process final scene
            if scene_frames:
                segment = self._process_scene_segment(
                    scene_frames, 
                    current_scene_start, 
                    duration
                )
                self.scene_segments.append(segment)
            
            cap.release()
            return self.scene_segments
            
        except Exception as e:
            logger.error(f"Error analyzing video scenes: {e}")
            return []
    
    def _calculate_color_histogram(self, frame: np.ndarray) -> np.ndarray:
        """Calculate color histogram for scene detection"""
        # Convert to HSV for better color representation
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # Calculate histogram
        hist_h = cv2.calcHist([hsv], [0], None, [50], [0, 180])
        hist_s = cv2.calcHist([hsv], [1], None, [60], [0, 256])
        hist_v = cv2.calcHist([hsv], [2], None, [60], [0, 256])
        
        # Normalize and concatenate
        hist_h = cv2.normalize(hist_h, hist_h).flatten()
        hist_s = cv2.normalize(hist_s, hist_s).flatten()
        hist_v = cv2.normalize(hist_v, hist_v).flatten()
        
        return np.concatenate([hist_h, hist_s, hist_v])
    
    def _calculate_motion_score(self, frame: np.ndarray) -> float:
        """Calculate motion score using frame differencing"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        if len(self.frame_buffer) > 0:
            prev_gray = cv2.cvtColor(self.frame_buffer[-1], cv2.COLOR_BGR2GRAY)
            
            # Calculate optical flow
            flow = cv2.calcOpticalFlowFarneback(
                prev_gray, gray, None, 
                0.5, 3, 15, 3, 5, 1.2, 0
            )
            
            # Calculate magnitude
            mag, _ = cv2.cartToPolar(flow[..., 0], flow[..., 1])
            motion_score = np.mean(mag) / 20.0  # Normalize to 0-1
            motion_score = min(1.0, motion_score)
        else:
            motion_score = 0.0
        
        self.frame_buffer.append(frame)
        return motion_score
    
    def _process_scene_segment(self, 
                             scene_frames: List[Dict], 
                             start_time: float, 
                             end_time: float) -> SceneSegment:
        """Process frames in a scene to extract segment information"""
        # Calculate average motion
        avg_motion = np.mean([f['motion'] for f in scene_frames])
        
        # Determine scene type
        if avg_motion < 0.1:
            scene_type = 'static'
        elif avg_motion > 0.5:
            scene_type = 'dynamic'
        else:
            scene_type = 'transition'
        
        # Extract dominant colors
        dominant_colors = self._extract_dominant_colors(scene_frames[len(scene_frames)//2]['frame'])
        
        # Generate description
        description = f"{scene_type.capitalize()} scene with {', '.join(dominant_colors)} colors"
        if avg_motion > 0.3:
            description += f" and high motion (score: {avg_motion:.2f})"
        
        return SceneSegment(
            start_time=start_time,
            end_time=end_time,
            scene_type=scene_type,
            dominant_colors=dominant_colors,
            motion_level=avg_motion,
            object_persistence={},  # Would need object detection to fill this
            description=description
        )
    
    def _extract_dominant_colors(self, frame: np.ndarray, k: int = 3) -> List[str]:
        """Extract dominant colors from frame"""
        # Resize for faster processing
        small_frame = cv2.resize(frame, (150, 150))
        
        # Reshape to pixel array
        pixels = small_frame.reshape((-1, 3))
        
        # K-means clustering
        from sklearn.cluster import KMeans
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        kmeans.fit(pixels)
        
        # Get cluster centers (dominant colors)
        colors = kmeans.cluster_centers_
        
        # Map to color names
        color_names = []
        for color in colors:
            color_name = self._rgb_to_name(color)
            color_names.append(color_name)
        
        return color_names
    
    def _rgb_to_name(self, rgb: np.ndarray) -> str:
        """Convert RGB to descriptive color representation"""
        r, g, b = rgb
        
        # Create a descriptive color string based on RGB values
        # This avoids hardcoded color names and provides more flexibility
        
        # Determine brightness
        brightness = (r + g + b) / 3
        
        # Create descriptive color based on dominant channel
        if brightness < 30:
            return f"very_dark_rgb({int(r)},{int(g)},{int(b)})"
        elif brightness > 225:
            return f"very_bright_rgb({int(r)},{int(g)},{int(b)})"
        else:
            # Determine dominant color channel
            max_val = max(r, g, b)
            if max_val == r and r > g + 30 and r > b + 30:
                return f"reddish_rgb({int(r)},{int(g)},{int(b)})"
            elif max_val == g and g > r + 30 and g > b + 30:
                return f"greenish_rgb({int(r)},{int(g)},{int(b)})"
            elif max_val == b and b > r + 30 and b > g + 30:
                return f"bluish_rgb({int(r)},{int(g)},{int(b)})"
            else:
                return f"mixed_rgb({int(r)},{int(g)},{int(b)})"
    
    def find_scene_transitions(self, threshold: float = 0.7) -> List[Tuple[float, float]]:
        """Find major scene transitions in the video"""
        transitions = []
        
        for i in range(1, len(self.scene_segments)):
            prev_scene = self.scene_segments[i-1]
            curr_scene = self.scene_segments[i]
            
            # Check if this is a major transition
            if (prev_scene.scene_type != curr_scene.scene_type or
                set(prev_scene.dominant_colors) != set(curr_scene.dominant_colors)):
                transitions.append((prev_scene.end_time, curr_scene.start_time))
        
        return transitions
    
    def get_scene_at_timestamp(self, timestamp: float) -> Optional[SceneSegment]:
        """Get the scene segment at a specific timestamp"""
        for segment in self.scene_segments:
            if segment.start_time <= timestamp <= segment.end_time:
                return segment
        return None