"""
CLIP + Optical Flow Embedding Service
Combines visual CLIP embeddings with motion features for enhanced video search
"""

import logging
import cv2
import numpy as np
import torch
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
import asyncio
from transformers import CLIPProcessor, CLIPModel
from PIL import Image
from app.core.config import settings

logger = logging.getLogger(__name__)

@dataclass
class EnhancedEmbedding:
    """Combined embedding with CLIP visual features and optical flow motion"""
    frame_id: str
    timestamp: float
    clip_embedding: np.ndarray
    optical_flow_features: Optional[np.ndarray]
    combined_embedding: np.ndarray
    metadata: Dict[str, Any]

class CLIPOpticalFlowService:
    """Service for generating CLIP + optical flow embeddings"""
    
    def __init__(self):
        self.clip_enabled = settings.ENABLE_CLIP_EMBEDDINGS
        self.optical_flow_enabled = settings.ENABLE_OPTICAL_FLOW
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # Initialize CLIP model
        self.clip_model = None
        self.clip_processor = None
        if self.clip_enabled:
            try:
                self.clip_model = CLIPModel.from_pretrained(settings.CLIP_MODEL_NAME)
                self.clip_processor = CLIPProcessor.from_pretrained(settings.CLIP_MODEL_NAME)
                self.clip_model.to(self.device)
                self.clip_model.eval()
                logger.info(f"✅ CLIP model loaded: {settings.CLIP_MODEL_NAME}")
            except Exception as e:
                logger.error(f"Failed to load CLIP model: {e}")
                self.clip_enabled = False
        
        # Optical flow parameters
        self.flow_method = settings.OPTICAL_FLOW_METHOD
        self.prev_frame = None
        
    def compute_clip_embedding(self, image_path: str) -> Optional[np.ndarray]:
        """Compute CLIP embedding for an image"""
        if not self.clip_enabled or not self.clip_model:
            return None
            
        try:
            # Load and preprocess image
            image = Image.open(image_path).convert("RGB")
            inputs = self.clip_processor(images=image, return_tensors="pt")
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get CLIP features
            with torch.no_grad():
                image_features = self.clip_model.get_image_features(**inputs)
                
            # Normalize and convert to numpy
            image_features = image_features / image_features.norm(dim=-1, keepdim=True)
            embedding = image_features.cpu().numpy().flatten()
            
            return embedding
            
        except Exception as e:
            logger.error(f"Error computing CLIP embedding: {e}")
            return None
    
    def compute_optical_flow_features(self, frame: np.ndarray, prev_frame: Optional[np.ndarray] = None) -> Optional[np.ndarray]:
        """Compute optical flow features between consecutive frames"""
        if not self.optical_flow_enabled or prev_frame is None:
            return None
            
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
            
            # Compute optical flow
            if self.flow_method == "farneback":
                flow = cv2.calcOpticalFlowFarneback(
                    prev_gray, gray, None,
                    pyr_scale=0.5, levels=3, winsize=15,
                    iterations=3, poly_n=5, poly_sigma=1.2, flags=0
                )
            else:
                # Dense optical flow alternative
                flow = cv2.calcOpticalFlowFarneback(
                    prev_gray, gray, None,
                    pyr_scale=0.5, levels=5, winsize=20,
                    iterations=5, poly_n=7, poly_sigma=1.5, flags=0
                )
            
            # Extract motion features
            mag, ang = cv2.cartToPolar(flow[..., 0], flow[..., 1])
            
            # Create feature vector
            features = []
            
            # Global motion statistics
            features.extend([
                np.mean(mag),           # Average motion magnitude
                np.std(mag),            # Motion variance
                np.max(mag),            # Maximum motion
                np.percentile(mag, 95), # 95th percentile motion
            ])
            
            # Directional motion histogram (8 bins)
            hist_bins = 8
            angle_hist, _ = np.histogram(ang[mag > 0.5], bins=hist_bins, range=(0, 2*np.pi))
            angle_hist = angle_hist / (np.sum(angle_hist) + 1e-6)  # Normalize
            features.extend(angle_hist.tolist())
            
            # Spatial motion distribution (divide frame into 4x4 grid)
            h, w = mag.shape
            grid_h, grid_w = 4, 4
            for i in range(grid_h):
                for j in range(grid_w):
                    y1, y2 = i * h // grid_h, (i + 1) * h // grid_h
                    x1, x2 = j * w // grid_w, (j + 1) * w // grid_w
                    grid_mag = mag[y1:y2, x1:x2]
                    features.append(np.mean(grid_mag))
            
            return np.array(features, dtype=np.float32)
            
        except Exception as e:
            logger.error(f"Error computing optical flow: {e}")
            return None
    
    async def process_keyframe(self, 
                             frame_path: str, 
                             frame: np.ndarray,
                             timestamp: float,
                             prev_frame: Optional[np.ndarray] = None,
                             metadata: Dict = None) -> Optional[EnhancedEmbedding]:
        """Process a keyframe to generate enhanced embeddings"""
        try:
            # Compute CLIP embedding
            clip_embedding = None
            if self.clip_enabled:
                clip_embedding = await asyncio.get_event_loop().run_in_executor(
                    None, self.compute_clip_embedding, frame_path
                )
            
            # Compute optical flow features
            flow_features = None
            if self.optical_flow_enabled and prev_frame is not None:
                flow_features = self.compute_optical_flow_features(frame, prev_frame)
            
            # Combine embeddings
            combined = self._combine_embeddings(clip_embedding, flow_features)
            
            if combined is None:
                return None
            
            return EnhancedEmbedding(
                frame_id=f"frame_{timestamp}",
                timestamp=timestamp,
                clip_embedding=clip_embedding,
                optical_flow_features=flow_features,
                combined_embedding=combined,
                metadata=metadata or {}
            )
            
        except Exception as e:
            logger.error(f"Error processing keyframe: {e}")
            return None
    
    def _combine_embeddings(self, 
                          clip_embedding: Optional[np.ndarray], 
                          flow_features: Optional[np.ndarray]) -> Optional[np.ndarray]:
        """Combine CLIP and optical flow features with weighted fusion"""
        embeddings = []
        weights = []
        
        if clip_embedding is not None:
            embeddings.append(clip_embedding)
            weights.append(settings.CLIP_WEIGHT)
        
        if flow_features is not None:
            # Normalize flow features to similar scale as CLIP
            flow_norm = flow_features / (np.linalg.norm(flow_features) + 1e-6)
            # Pad or truncate to match CLIP dimension if needed
            if clip_embedding is not None:
                target_dim = len(clip_embedding)
                if len(flow_norm) < target_dim:
                    flow_norm = np.pad(flow_norm, (0, target_dim - len(flow_norm)))
                else:
                    flow_norm = flow_norm[:target_dim]
            embeddings.append(flow_norm)
            weights.append(settings.OPTICAL_FLOW_WEIGHT)
        
        if not embeddings:
            return None
        
        # Weighted combination
        if len(embeddings) == 1:
            return embeddings[0]
        
        # Normalize weights
        weights = np.array(weights)
        weights = weights / np.sum(weights)
        
        # Weighted sum
        combined = np.zeros_like(embeddings[0])
        for emb, w in zip(embeddings, weights):
            combined += w * emb
        
        # Final normalization
        combined = combined / (np.linalg.norm(combined) + 1e-6)
        
        return combined
    
    def compute_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """Compute similarity between two embeddings"""
        if settings.SIMILARITY_METRIC == "cosine":
            return float(np.dot(embedding1, embedding2))
        elif settings.SIMILARITY_METRIC == "euclidean":
            return float(1.0 / (1.0 + np.linalg.norm(embedding1 - embedding2)))
        elif settings.SIMILARITY_METRIC == "dot":
            return float(np.dot(embedding1, embedding2))
        else:
            # Default to cosine
            return float(np.dot(embedding1, embedding2))
    
    async def process_video_keyframes(self, 
                                    keyframes: List[Dict],
                                    video_path: str) -> List[EnhancedEmbedding]:
        """Process all keyframes from a video"""
        embeddings = []
        prev_frame = None
        
        # Open video for reading frames
        cap = cv2.VideoCapture(video_path)
        
        try:
            for keyframe in keyframes:
                timestamp = keyframe['timestamp']
                frame_path = keyframe.get('frame_path')
                
                # Seek to timestamp
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_number = int(timestamp * fps)
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                
                ret, frame = cap.read()
                if not ret:
                    continue
                
                # Process keyframe
                embedding = await self.process_keyframe(
                    frame_path=frame_path,
                    frame=frame,
                    timestamp=timestamp,
                    prev_frame=prev_frame,
                    metadata=keyframe.get('metadata', {})
                )
                
                if embedding:
                    embeddings.append(embedding)
                
                prev_frame = frame
                
        finally:
            cap.release()
        
        return embeddings
    
    def search_similar_frames(self, 
                            query_embedding: np.ndarray, 
                            frame_embeddings: List[EnhancedEmbedding],
                            top_k: int = None) -> List[Tuple[EnhancedEmbedding, float]]:
        """Search for similar frames using combined embeddings"""
        if top_k is None:
            top_k = settings.MAX_SEARCH_RESULTS
            
        similarities = []
        
        for frame_emb in frame_embeddings:
            similarity = self.compute_similarity(query_embedding, frame_emb.combined_embedding)
            if similarity >= settings.CONFIDENCE_THRESHOLD:
                similarities.append((frame_emb, similarity))
        
        # Sort by similarity
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        return similarities[:top_k]