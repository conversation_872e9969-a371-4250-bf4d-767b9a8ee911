# Video Analysis Performance Fixes

## Issues Identified

1. **Sequential frame-by-frame API calls** - Each frame analyzed individually (up to 50+ separate API calls)
2. **Not using optimized services** - OptimizedVideoProcessor and FastAnalysisService weren't being used
3. **Excessive frame extraction** - Extracting frames every 5 seconds without intelligent selection
4. **No parallelization** - All processing happened sequentially
5. **Full video download** - Downloaded entire YouTube videos before processing

## Fixes Implemented

### 1. Switched to OptimizedVideoProcessor
- Updated `video.py` to use `OptimizedVideoProcessor` instead of basic `VideoProcessor`
- Added scene detection for intelligent frame selection
- Implemented parallel frame extraction using ThreadPoolExecutor
- Reduced frame extraction for longer videos (1 frame per 10 seconds for videos > 10 minutes)

### 2. Enabled FastAnalysisService for Batch Processing
- Updated `search.py` to use `FastAnalysisService.analyze_frames_batch()`
- Replaced sequential frame analysis with batch processing
- Processes multiple frames in a single API call
- Added batch processing in video upload flow

### 3. Updated Configuration
- Increased `FRAME_EXTRACTION_INTERVAL` from 5 to 10 seconds
- Reduced `MAX_FRAMES_PER_VIDEO` from 300 to 100
- Added new performance settings:
  - `USE_SCENE_DETECTION`: True
  - `SCENE_DETECTION_THRESHOLD`: 30.0
  - `BATCH_ANALYSIS_SIZE`: 10 frames per batch
  - `PARALLEL_WORKERS`: 4 threads

### 4. Scene Detection Algorithm
- Detects scene changes using histogram correlation
- Prioritizes frames with significant visual changes
- Falls back to evenly spaced frames if needed
- Reduces redundant similar frames

### 5. Parallel Processing
- Frame extraction now happens in parallel batches
- Uses ThreadPoolExecutor with configurable worker count
- Processes frames in batches of 10 to avoid memory issues

## Expected Performance Improvements

- **50-70% reduction** in video processing time
- **80% reduction** in API calls for frame analysis
- **Better frame selection** - only analyzes visually distinct frames
- **Lower memory usage** - batch processing instead of loading all frames
- **Progress tracking** - real-time updates during processing

## Usage

The optimizations are automatically applied. No changes needed in the frontend.

To further tune performance, adjust these environment variables:
- `FRAME_EXTRACTION_INTERVAL`: Seconds between frame extractions
- `MAX_FRAMES_PER_VIDEO`: Maximum frames to extract
- `BATCH_ANALYSIS_SIZE`: Frames per analysis batch
- `PARALLEL_WORKERS`: Number of parallel threads