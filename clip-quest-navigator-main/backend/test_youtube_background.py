#!/usr/bin/env python3
"""Test script for YouTube background processing"""

import asyncio
import requests
import time
import sys

BASE_URL = "http://localhost:8000/api"

def test_youtube_background_processing():
    """Test the YouTube video background processing"""
    
    # Test YouTube URL
    youtube_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"  # <PERSON> - Never Gonna Give You Up (short video)
    
    print(f"Testing YouTube background processing with URL: {youtube_url}")
    
    # Step 1: Submit YouTube video for processing
    print("\n1. Submitting YouTube video for processing...")
    response = requests.post(
        f"{BASE_URL}/video/youtube",
        json={
            "youtube_url": youtube_url,
            "use_optimizer": True,
            "enable_visual_search": True
        }
    )
    
    if response.status_code != 200:
        print(f"Error submitting video: {response.status_code}")
        print(response.json())
        return
    
    result = response.json()
    video_id = result.get("video_id")
    print(f"Video submitted successfully! Video ID: {video_id}")
    print(f"Initial status: {result.get('status')}")
    print(f"Message: {result.get('message')}")
    
    # Step 2: Poll status endpoint
    print("\n2. Polling for processing status...")
    max_attempts = 60  # Poll for up to 5 minutes
    poll_interval = 5  # seconds
    
    for i in range(max_attempts):
        time.sleep(poll_interval)
        
        # Get status
        status_response = requests.get(f"{BASE_URL}/video/{video_id}/status")
        
        if status_response.status_code != 200:
            print(f"Error getting status: {status_response.status_code}")
            continue
        
        status_data = status_response.json()
        progress = status_data.get("progress", 0)
        status = status_data.get("status", "unknown")
        message = status_data.get("message", "")
        stage = status_data.get("stage", "")
        
        print(f"\r[{i+1}/{max_attempts}] Status: {status} | Progress: {progress}% | Stage: {stage} | {message}", end="")
        
        if status in ["completed", "failed"]:
            print("\n")
            break
    
    # Step 3: Check final results
    print("\n3. Checking final video details...")
    video_response = requests.get(f"{BASE_URL}/video/{video_id}")
    
    if video_response.status_code == 200:
        video_data = video_response.json()
        print(f"Video Title: {video_data.get('title')}")
        print(f"Duration: {video_data.get('duration')} seconds")
        print(f"Has Transcript: {video_data.get('has_transcript')}")
        print(f"Frame Count: {video_data.get('frame_count')}")
        print(f"Sections: {len(video_data.get('sections', []))}")
        
        # Check sections
        if video_data.get('sections'):
            print("\nVideo Sections:")
            for idx, section in enumerate(video_data['sections'][:3]):  # Show first 3 sections
                print(f"  {idx+1}. {section.get('title')} - {section.get('start_time')}")
    else:
        print(f"Error getting video details: {video_response.status_code}")
    
    print("\nTest completed!")

if __name__ == "__main__":
    try:
        test_youtube_background_processing()
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nError during test: {e}")
        sys.exit(1)