#!/usr/bin/env python3
"""
Test script for native video search functionality
"""

import asyncio
import os
import sys
from pathlib import Path

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent))

from app.services.native_video_search import NativeVideoSearchService


async def test_native_search():
    """Test native video search with a sample video"""
    
    # You'll need to update this path to a test video
    test_video_path = "./uploads/test_video.mp4"
    
    if not os.path.exists(test_video_path):
        print(f"Test video not found at {test_video_path}")
        print("Please provide a test video at the specified path")
        return
    
    # Initialize service
    print("Initializing Native Video Search Service...")
    service = NativeVideoSearchService()
    
    try:
        # Test 1: General search
        print("\n=== Test 1: General Search ===")
        print("Searching for 'person'...")
        clips = await service.search_visual_content(
            video_path=test_video_path,
            query="person",
            search_type="general"
        )
        print(f"Found {len(clips)} clips")
        for i, clip in enumerate(clips[:3]):  # Show first 3
            print(f"\nClip {i+1}:")
            print(f"  Time: {clip.to_dict()['timestamp_formatted']} - {clip.to_dict()['end_timestamp_formatted']}")
            print(f"  Confidence: {clip.confidence:.1%}")
            print(f"  Description: {clip.description[:100]}...")
        
        # Test 2: Color + Object search
        print("\n\n=== Test 2: Color + Object Search ===")
        print("Searching for 'blue shirt'...")
        clips = await service.search_color_object_combo(
            video_path=test_video_path,
            color="blue",
            object_type="shirt"
        )
        print(f"Found {len(clips)} clips with blue shirts")
        
        # Test 3: Counting
        print("\n\n=== Test 3: Counting Elements ===")
        print("Counting unique people in video...")
        count_result = await service.count_visual_elements(
            video_path=test_video_path,
            element="people",
            count_type="unique"
        )
        print(f"Total unique people: {count_result['total_count']}")
        print(f"Temporal pattern: {count_result.get('temporal_pattern', 'N/A')}")
        
        # Test 4: Text search
        print("\n\n=== Test 4: Text Search ===")
        print("Searching for text/signs...")
        clips = await service.search_text_in_video(
            video_path=test_video_path,
            text_query="sign"
        )
        print(f"Found {len(clips)} clips with text/signs")
        
        # Test 5: Scene search
        print("\n\n=== Test 5: Scene Type Search ===")
        print("Searching for outdoor scenes...")
        clips = await service.search_by_scene_type(
            video_path=test_video_path,
            scene_type="outdoor"
        )
        print(f"Found {len(clips)} outdoor scenes")
        
        # Cleanup
        print("\n\nCleaning up uploaded video...")
        success = await service.cleanup_upload(test_video_path)
        print(f"Cleanup {'successful' if success else 'failed'}")
        
    except Exception as e:
        print(f"\nError during testing: {e}")
        import traceback
        traceback.print_exc()
        
        # Attempt cleanup on error
        try:
            await service.cleanup_upload(test_video_path)
        except:
            pass


if __name__ == "__main__":
    # Check for API key
    if not os.getenv("GEMINI_API_KEY"):
        print("ERROR: GEMINI_API_KEY environment variable not set")
        sys.exit(1)
    
    # Run tests
    asyncio.run(test_native_search())