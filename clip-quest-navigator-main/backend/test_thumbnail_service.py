#!/usr/bin/env python3
"""
Test script to verify thumbnail generation functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.thumbnail_service import ThumbnailService
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_thumbnail_service():
    """Test thumbnail service functionality"""
    
    print("🔍 Testing Thumbnail Service...")
    
    try:
        # Initialize thumbnail service
        thumbnail_service = ThumbnailService()
        print("✅ ThumbnailService initialized successfully")
        
        # Check if OpenCV is available
        try:
            import cv2
            print(f"✅ OpenCV version: {cv2.__version__}")
        except ImportError:
            print("❌ OpenCV not available")
            return False
        
        # Get thumbnail stats
        stats = thumbnail_service.get_thumbnail_stats()
        print(f"📊 Thumbnail Stats: {stats}")
        
        # Test thumbnail specs
        print("📏 Available thumbnail specs:")
        for name, spec in thumbnail_service.specs.items():
            print(f"  - {name}: {spec.width}x{spec.height} (quality: {spec.quality})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing thumbnail service: {e}")
        return False

def test_static_file_serving():
    """Test if static file endpoints are accessible"""
    
    print("\n🌐 Testing Static File Endpoints...")
    
    import requests
    
    try:
        # Test thumbnails endpoint
        response = requests.get("http://localhost:8000/api/thumbnails/", timeout=5)
        print(f"📁 /api/thumbnails/ status: {response.status_code}")
        
        # Test frames endpoint  
        response = requests.get("http://localhost:8000/api/frames/", timeout=5)
        print(f"📁 /api/frames/ status: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing static endpoints: {e}")
        return False

if __name__ == "__main__":
    print("🎬 VidSearch Thumbnail System Test\n")
    
    # Test thumbnail service
    service_ok = test_thumbnail_service()
    
    # Test static file serving
    static_ok = test_static_file_serving()
    
    print(f"\n📋 Test Results:")
    print(f"  Thumbnail Service: {'✅ PASS' if service_ok else '❌ FAIL'}")
    print(f"  Static File Serving: {'✅ PASS' if static_ok else '❌ FAIL'}")
    
    if service_ok and static_ok:
        print("\n🎉 Thumbnail system is ready!")
    else:
        print("\n⚠️  Thumbnail system needs fixes")
