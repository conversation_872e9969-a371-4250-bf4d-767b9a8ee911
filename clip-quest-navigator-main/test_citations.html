<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Citation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .chat-message {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .citation {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            font-size: 12px;
            background-color: #2563eb;
            color: white;
            border-radius: 50%;
            margin: 0 2px;
            cursor: pointer;
            border: none;
            vertical-align: super;
            position: relative;
        }
        .citation:hover {
            background-color: #1d4ed8;
        }
        .tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: #1f2937;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 11px;
            white-space: nowrap;
            margin-bottom: 5px;
            display: none;
            z-index: 1000;
        }
        .citation:hover .tooltip {
            display: block;
        }
        .tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 4px solid transparent;
            border-top-color: #1f2937;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
        }
        .api-test {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>🔍 Citation Functionality Test</h1>
    
    <div class="test-section">
        <h2>1. Manual Citation Test</h2>
        <div class="chat-message">
            <p>This is a test message with citations. Google Cloud is focusing on three key areas 
            <button class="citation" onclick="jumpToTime(15)">
                1
                <div class="tooltip">Jump to 00:15 - "innovating in three key areas..."</div>
            </button>
            and introducing new AI agents 
            <button class="citation" onclick="jumpToTime(30)">
                2
                <div class="tooltip">Jump to 00:30 - "build your agents, our Agent Engine..."</div>
            </button>
            for developers.</p>
        </div>
    </div>

    <div class="test-section">
        <h2>2. API Response Test</h2>
        <button onclick="testChatAPI()">Test Chat API</button>
        <div id="api-result" class="api-test">Click button to test...</div>
    </div>

    <div class="test-section">
        <h2>3. Citation Parsing Test</h2>
        <button onclick="testCitationParsing()">Test Citation Parsing</button>
        <div id="parsing-result" class="api-test">Click button to test...</div>
    </div>

    <script>
        function jumpToTime(seconds) {
            alert(`Would jump to ${seconds} seconds (${formatTime(seconds)})`);
        }

        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = seconds % 60;
            return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        async function testChatAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = 'Testing API...';
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/chat/message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        video_id: 11,
                        message: 'What are the main topics in the first minute?'
                    })
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <strong>✅ API Response:</strong><br>
                    Citations: ${data.citations ? data.citations.length : 0}<br>
                    Response length: ${data.response ? data.response.length : 0} chars<br>
                    Contains timestamps: ${data.response && data.response.includes('[') ? 'Yes' : 'No'}<br>
                    <br>
                    <strong>First 200 chars:</strong><br>
                    ${data.response ? data.response.substring(0, 200) + '...' : 'No response'}
                `;
            } catch (error) {
                resultDiv.innerHTML = `❌ Error: ${error.message}`;
            }
        }

        function testCitationParsing() {
            const resultDiv = document.getElementById('parsing-result');
            
            // Test text with timestamps
            const testText = "Google Cloud is focusing on three areas [00:15] and introducing AI agents [00:30] for developers.";
            const timestampPattern = /\[(\d{1,2}:\d{2}(?::\d{2})?)\]/g;
            
            const matches = [];
            let match;
            while ((match = timestampPattern.exec(testText)) !== null) {
                matches.push({
                    timestamp: match[1],
                    position: match.index,
                    fullMatch: match[0]
                });
            }
            
            resultDiv.innerHTML = `
                <strong>✅ Citation Parsing Test:</strong><br>
                Test text: "${testText}"<br>
                Matches found: ${matches.length}<br>
                <br>
                ${matches.map((m, i) => `
                    ${i + 1}. Timestamp: ${m.timestamp}, Position: ${m.position}, Match: "${m.fullMatch}"
                `).join('<br>')}
            `;
        }
    </script>
</body>
</html>
