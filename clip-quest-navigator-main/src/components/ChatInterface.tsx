
import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, User } from 'lucide-react';
import { apiService } from '@/services/api';
import { useToast } from '@/hooks/use-toast';
import FormattedMessage from './FormattedMessage';

interface Citation {
  text: string;
  time: number;
  timestamp: string;
  citation_id: number;
}

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  citations?: Citation[];
}

interface ChatInterfaceProps {
  videoId: number;
  videoUrl: string;
  onTimeJump: (time: number) => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ videoId, videoUrl, onTimeJump }) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: "Hello! What would you like to know about this video?",
      sender: 'ai',
      timestamp: new Date(),
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputMessage.trim()) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, newMessage]);
    const currentMessage = inputMessage;
    setInputMessage('');
    setIsTyping(true);

    try {
      console.log(`🤖 Sending chat message for video ${videoId}: "${currentMessage}"`);
      
      const response = await apiService.sendChatMessage(videoId, currentMessage);
      
      console.log('📝 Chat response received:', {
        messageId: response.message_id,
        hasResponse: !!response.response,
        citationsCount: response.citations?.length || 0,
        citations: response.citations
      });

      const aiResponse: Message = {
        id: response.message_id.toString(),
        content: response.response,
        sender: 'ai',
        timestamp: new Date(),
        citations: response.citations || []
      };

      setMessages(prev => [...prev, aiResponse]);
      
      // Log successful citation extraction
      if (response.citations && response.citations.length > 0) {
        console.log('✅ Citations extracted:', response.citations.map(c => ({
          text: c.text?.substring(0, 50) + '...',
          time: c.time,
          timestamp: c.timestamp
        })));
      } else {
        console.log('⚠️ No citations found in response');
      }
      
    } catch (error) {
      console.error('❌ Chat error:', error);
      
      // More detailed error handling
      let errorMessage = "Failed to send message";
      if (error instanceof Error) {
        if (error.message.includes('404')) {
          errorMessage = "Video not found. Please make sure the video is uploaded and processed.";
        } else if (error.message.includes('500')) {
          errorMessage = "Server error. The video may need more processing time.";
        } else {
          errorMessage = error.message;
        }
      }
      
      toast({
        title: "Chat Error",
        description: errorMessage,
        variant: "destructive",
      });

      // Add error message
      const errorResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: "I'm sorry, I encountered an error while processing your message. Please try again, or make sure the video has been properly uploaded and processed.",
        sender: 'ai',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, errorResponse]);
    } finally {
      setIsTyping(false);
    }
  };




  return (
    <div className="flex flex-col h-full">
      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto space-y-4 pr-2">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div className={`flex gap-3 max-w-[85%] ${message.sender === 'user' ? 'flex-row-reverse' : ''}`}>
              {/* Avatar */}
              <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                message.sender === 'user'
                  ? 'bg-primary'
                  : 'bg-muted'
              }`}>
                {message.sender === 'user' ? (
                  <User className="w-4 h-4 text-primary-foreground" />
                ) : (
                  <Bot className="w-4 h-4 text-muted-foreground" />
                )}
              </div>

              {/* Message Content */}
              <div className={`rounded-lg p-3 ${
                message.sender === 'user'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted text-foreground'
              }`}>
                {message.sender === 'ai' && message.citations ? (
                  <FormattedMessage
                    content={message.content}
                    citations={message.citations}
                    onTimeJump={onTimeJump}
                  />
                ) : (
                  <p className="text-sm leading-relaxed">{message.content}</p>
                )}

                <p className="text-xs text-muted-foreground mt-3">
                  {message.timestamp.toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>
            </div>
          </div>
        ))}

        {/* Typing Indicator */}
        {isTyping && (
          <div className="flex justify-start">
            <div className="flex gap-3 max-w-[85%]">
              <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center flex-shrink-0">
                <Bot className="w-4 h-4 text-muted-foreground" />
              </div>
              <div className="bg-muted text-foreground rounded-lg p-3">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-foreground rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Form */}
      <form onSubmit={handleSendMessage} className="mt-4">
        <div className="flex gap-2">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            placeholder="Ask anything about this video..."
            className="flex-1 px-4 py-3 bg-input border border-border rounded-lg text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
            disabled={isTyping}
          />
          <button
            type="submit"
            disabled={!inputMessage.trim() || isTyping}
            className="px-4 py-3 bg-primary hover:bg-primary/90 disabled:bg-muted disabled:text-muted-foreground disabled:cursor-not-allowed text-primary-foreground rounded-lg transition-all duration-300 transform hover:scale-105"
          >
            <Send className="w-5 h-5" />
          </button>
        </div>
      </form>
    </div>
  );
};

export default ChatInterface;
