<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Citations Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .chat-message {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .citation {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            font-size: 12px;
            background-color: #2563eb;
            color: white;
            border-radius: 50%;
            margin: 0 2px;
            cursor: pointer;
            border: none;
            vertical-align: super;
            position: relative;
        }
        .citation:hover {
            background-color: #1d4ed8;
        }
        .tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: #1f2937;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 11px;
            white-space: nowrap;
            margin-bottom: 5px;
            display: none;
            z-index: 1000;
            max-width: 300px;
            white-space: normal;
        }
        .citation:hover .tooltip {
            display: block;
        }
        .api-result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🔍 Chat Citations Functionality Test</h1>
    
    <div class="test-section">
        <h2>1. API Connection Test</h2>
        <button onclick="testAPIConnection()">Test Backend Connection</button>
        <div id="connection-status" class="api-result">Click button to test...</div>
    </div>

    <div class="test-section">
        <h2>2. Chat API Test</h2>
        <button onclick="testChatAPI()">Send Test Chat Message</button>
        <div id="chat-result" class="api-result">Click button to test...</div>
    </div>

    <div class="test-section">
        <h2>3. Citation Parsing Test</h2>
        <button onclick="testCitationParsing()">Test Citation Parsing</button>
        <div id="parsing-result" class="api-result">Click button to test...</div>
    </div>

    <div class="test-section">
        <h2>4. Live Citation Display Test</h2>
        <button onclick="testLiveCitations()">Test Live Citations</button>
        <div id="live-citations" class="chat-message">
            <div id="citation-display">Click button to load live citations...</div>
        </div>
    </div>

    <script>
        function jumpToTime(seconds) {
            alert(`Would jump to ${seconds} seconds (${formatTime(seconds)})`);
        }

        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = seconds % 60;
            return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        async function testAPIConnection() {
            const statusDiv = document.getElementById('connection-status');
            statusDiv.innerHTML = 'Testing connection...';
            
            try {
                const response = await fetch('http://localhost:8000/health');
                const data = await response.json();
                
                statusDiv.innerHTML = `✅ Backend Connected
Status: ${data.status}
Database: ${data.database_status}
Services: ${JSON.stringify(data.services, null, 2)}`;
                statusDiv.className = 'api-result status success';
            } catch (error) {
                statusDiv.innerHTML = `❌ Connection Failed: ${error.message}`;
                statusDiv.className = 'api-result status error';
            }
        }

        async function testChatAPI() {
            const resultDiv = document.getElementById('chat-result');
            resultDiv.innerHTML = 'Sending chat message...';
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/chat/message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        video_id: 11,
                        message: 'What are the main features mentioned?'
                    })
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `✅ Chat API Response:
Response length: ${data.response ? data.response.length : 0} chars
Citations count: ${data.citations ? data.citations.length : 0}
Contains [timestamps]: ${data.response && data.response.includes('[') ? 'Yes' : 'No'}

First 200 chars:
${data.response ? data.response.substring(0, 200) + '...' : 'No response'}

First 3 citations:
${data.citations ? data.citations.slice(0, 3).map((c, i) => 
    `${i+1}. ${c.timestamp} -> ${c.text.substring(0, 50)}...`
).join('\n') : 'No citations'}`;
                resultDiv.className = 'api-result status success';
                
                // Store for live test
                window.testChatData = data;
                
            } catch (error) {
                resultDiv.innerHTML = `❌ Error: ${error.message}`;
                resultDiv.className = 'api-result status error';
            }
        }

        function testCitationParsing() {
            const resultDiv = document.getElementById('parsing-result');
            
            // Test text with timestamps
            const testText = "Google Cloud is focusing on three areas [00:15] and introducing AI agents [00:30] for developers [02:45].";
            const timestampPattern = /\[(\d{1,2}:\d{2}(?::\d{2})?)\]/g;
            
            const matches = [];
            let match;
            while ((match = timestampPattern.exec(testText)) !== null) {
                matches.push({
                    timestamp: match[1],
                    position: match.index,
                    fullMatch: match[0]
                });
            }
            
            resultDiv.innerHTML = `✅ Citation Parsing Test:
Test text: "${testText}"
Regex pattern: ${timestampPattern}
Matches found: ${matches.length}

${matches.map((m, i) => `
${i + 1}. Timestamp: ${m.timestamp}
   Position: ${m.position}
   Full match: "${m.fullMatch}"`).join('\n')}`;
            resultDiv.className = 'api-result status success';
        }

        function testLiveCitations() {
            const displayDiv = document.getElementById('citation-display');
            
            if (!window.testChatData) {
                displayDiv.innerHTML = '❌ No chat data available. Run Chat API Test first.';
                return;
            }
            
            const { response, citations } = window.testChatData;
            
            // Parse the response and create citations
            const timestampPattern = /\[(\d{1,2}:\d{2}(?::\d{2})?)\]/g;
            let processedText = response;
            let citationCount = 0;
            
            processedText = processedText.replace(timestampPattern, (match, timestamp) => {
                const citation = citations.find(c => c.timestamp === timestamp);
                if (citation) {
                    citationCount++;
                    return `<button class="citation" onclick="jumpToTime(${citation.time})" title="Jump to ${citation.timestamp}">
                        ${citation.citation_id}
                        <div class="tooltip">
                            🕐 ${citation.timestamp}<br>
                            ${citation.text.substring(0, 100)}...
                        </div>
                    </button>`;
                }
                return match;
            });
            
            displayDiv.innerHTML = `
                <div style="margin-bottom: 10px;">
                    <strong>✅ Live Citation Test Results:</strong><br>
                    Original response length: ${response.length} chars<br>
                    Citations processed: ${citationCount}<br>
                    Available citations: ${citations.length}
                </div>
                <div style="line-height: 1.6;">
                    ${processedText.substring(0, 800)}${response.length > 800 ? '...' : ''}
                </div>
            `;
        }
    </script>
</body>
</html>
